package com.baupos.platform.subscription.domain;

import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.UUID;

@Repository
public interface TenantSubscriptionRepository extends JpaRepository<TenantSubscription, UUID> {

    Slice<TenantSubscription> findByStatusAndCreateDateBefore(TenantSubscriptionStatus status, OffsetDateTime cutOffDate, Pageable pageable);

    @Query(value = """
            select distinct ts 
            from TenantSubscription ts
            where ts.status = com.baupos.platform.subscription.domain.TenantSubscriptionStatus.ACTIVE
            and not exists (select 1 from TenantInvoice ti where ti.issueDate >= :cutOffDate and ti.subscription = ts)
            """)
    Slice<TenantSubscription> findActiveSubscriptionsWithNoInvoiceAfterDate(OffsetDateTime cutOffDate, Pageable pageable);


    @Query(value = """
            select distinct ts 
            from TenantSubscription ts
            where ts.status = com.baupos.platform.subscription.domain.TenantSubscriptionStatus.ACTIVE
            and exists (select 1 from TenantInvoice ti 
                        where ti.status = com.baupos.platform.billing.domain.TenantInvoiceStatus.PAYMENT_PENDING 
                        and ti.dueDate < cast(now() as java.time.OffsetDateTime) 
                        and ti.subscription = ts)
            """)
    Slice<TenantSubscription> findActiveSubscriptionsWithOverDueInvoice(Pageable pageable);

    @Modifying
    @Query("""
            update versioned TenantSubscription ts
            set ts.status = :status, ts.updateDate = cast(now() as java.time.OffsetDateTime)
            where ts.id = :id
            """)
    void updateSubscriptionStatus(@Param("id") UUID subscriptionId, @Param("status") TenantSubscriptionStatus status);

    List<TenantSubscription> findByTenantId(UUID tenantId);
}
