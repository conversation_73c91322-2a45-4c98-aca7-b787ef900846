package com.baupos.platform.plan.service;

import com.baupos.platform.plan.domain.SubscriptionPlan;
import com.baupos.platform.plan.dto.SubscriptionPlanDto;
import org.springframework.stereotype.Component;

@Component
public class SubscriptionPlanMapper {

    public SubscriptionPlanDto mapToDto(SubscriptionPlan subscriptionPlan) {
        return SubscriptionPlanDto.builder()
                .id(subscriptionPlan.getId())
                .name(subscriptionPlan.getName())
                .priceMonthly(subscriptionPlan.getPriceMonthly())
                .limits(subscriptionPlan.getLimits())
                .build();
    }

}
