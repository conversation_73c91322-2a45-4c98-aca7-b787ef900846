package com.baupos.platform.plan.dto;

import com.baupos.platform.plan.domain.SubscriptionPlanCode;
import com.baupos.platform.plan.domain.SubscriptionPlanLimitsSto;
import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.math.BigDecimal;
import java.util.UUID;

@Builder
@Getter
@RequiredArgsConstructor
public class SubscriptionPlanDto {
    private final UUID id;
    private final SubscriptionPlanCode name;
    private final BigDecimal priceMonthly;
    private final SubscriptionPlanLimitsSto limits;
}
