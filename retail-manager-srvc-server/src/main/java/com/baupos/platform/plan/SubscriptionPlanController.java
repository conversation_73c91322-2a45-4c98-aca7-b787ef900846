package com.baupos.platform.plan;

import com.baupos.platform.plan.dto.SubscriptionPlanDto;
import com.baupos.platform.plan.service.SubscriptionPlanMapper;
import com.baupos.platform.plan.service.SubscriptionPlanService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/v1/subscription-plans")
@RequiredArgsConstructor
public class SubscriptionPlanController {

    private final SubscriptionPlanService subscriptionPlanService;
    private final SubscriptionPlanMapper subscriptionPlanMapper;

    @GetMapping("/")
    @PreAuthorize("isAuthenticated()")
    public List<SubscriptionPlanDto> listSubscriptionPlans() {
        return subscriptionPlanService.findAll().stream()
                .map(subscriptionPlanMapper::mapToDto)
                .toList();
    }

}
