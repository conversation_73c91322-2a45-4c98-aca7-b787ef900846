package com.baupos.retailmanager.auth.service;

import com.baupos.retailmanager.auth.service.sto.AuthUserDetailsSto;
import com.baupos.retailmanager.branch.dto.CreateBranchRequestDto;
import com.baupos.retailmanager.branch.dto.UpdateBranchRequestDto;
import com.baupos.retailmanager.branch.service.BranchService;
import com.baupos.retailmanager.customer.dto.CreateCustomerRequestDto;
import com.baupos.retailmanager.customer.dto.UpdateCustomerRequestDto;
import com.baupos.retailmanager.customer.service.CustomerService;
import com.baupos.retailmanager.employee.dto.CreateEmployeeRequestDto;
import com.baupos.retailmanager.employee.dto.UpdateEmployeeRequestDto;
import com.baupos.retailmanager.employee.service.EmployeeService;
import com.baupos.retailmanager.user.domain.Privilege;
import com.baupos.retailmanager.user.domain.ResourceType;
import com.baupos.retailmanager.user.domain.Role;
import com.baupos.retailmanager.user.dto.CreateUserRequestDto;
import com.baupos.retailmanager.user.dto.UpdateUserRequestDto;
import com.baupos.retailmanager.user.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.stereotype.Component;

import java.util.*;

import static org.apache.commons.collections4.CollectionUtils.isEmpty;

@Slf4j
@Component
@RequiredArgsConstructor
public class AuthorizationChecks {

    private final AuthenticationFacade authenticationFacade;
    private final UserService userService;
    private final EmployeeService employeeService;
    private final CustomerService customerService;
    private final BranchService branchService;

    public boolean isLoggedIn() {
        return authenticationFacade.getAuthenticatedUserDetails().isPresent();
    }

    public boolean canCreateEmployee(CreateEmployeeRequestDto createEmployeeRequest) {
        return hasPrivilege(Privilege.EMPLOYEE_CREATE)
                && hasPrivileges(createEmployeeRequest.getPrivileges()); // Only allow setting privileges that the authenticated user has
    }

    public boolean canReadEmployees() {
        return hasPrivilege(Privilege.EMPLOYEE_READ);
    }

    public boolean canReadEmployee(UUID employeeId) {
        return hasGrantedResource(ResourceType.TENANT, getTenantIdFromEmployee(employeeId)) && hasPrivilege(Privilege.EMPLOYEE_READ);
    }
    
    public boolean canUpdateEmployee(UUID employeeId, UpdateEmployeeRequestDto updateEmployeeRequest) {
        return hasGrantedResource(ResourceType.TENANT, getTenantIdFromEmployee(employeeId)) && hasPrivilege(Privilege.EMPLOYEE_UPDATE);
    }

    public boolean canDeleteEmployee(UUID employeeId) {
        return hasGrantedResource(ResourceType.TENANT, getTenantIdFromEmployee(employeeId)) && hasPrivilege(Privilege.EMPLOYEE_DELETE);
    }

    public boolean canCreateCustomer(CreateCustomerRequestDto createCustomerRequest) {
        return hasPrivilege(Privilege.CUSTOMER_CREATE);
    }

    public boolean canReadCustomers() {
        return hasPrivilege(Privilege.CUSTOMER_READ);
    }

    public boolean canReadCustomer(UUID customerId) {
        return hasGrantedResource(ResourceType.TENANT, getTenantIdFromCustomer(customerId)) && hasPrivilege(Privilege.CUSTOMER_READ);
    }

    public boolean canUpdateCustomer(UUID customerId, UpdateCustomerRequestDto updateCustomerRequest) {
        return hasGrantedResource(ResourceType.TENANT, getTenantIdFromCustomer(customerId)) && hasPrivilege(Privilege.CUSTOMER_UPDATE);
    }

    public boolean canDeleteCustomer(UUID customerId) {
        return hasGrantedResource(ResourceType.TENANT, getTenantIdFromCustomer(customerId)) && hasPrivilege(Privilege.CUSTOMER_DELETE);
    }

    public boolean canCreateBranch(CreateBranchRequestDto createBranchRequest) {
        return hasPrivilege(Privilege.BRANCH_CREATE);
    }

    public boolean canReadBranches() {
        return hasPrivilege(Privilege.BRANCH_READ);
    }

    public boolean canReadBranch(UUID branchId) {
        return hasGrantedResource(ResourceType.BRANCH, String.valueOf(branchId))
                && (hasPrivilege(Privilege.BRANCH_READ) || hasPrivilege(Privilege.BRANCH_CREATE));
    }

    public boolean canUpdateBranch(UUID branchId, UpdateBranchRequestDto updateBranchRequest) {
        return hasGrantedResource(ResourceType.BRANCH, String.valueOf(branchId))
                && hasPrivilege(Privilege.BRANCH_UPDATE);
    }

    public boolean canDeleteBranch(UUID branchId) {
        return hasGrantedResource(ResourceType.BRANCH, String.valueOf(branchId))
                && hasPrivilege(Privilege.BRANCH_DELETE);
    }

    public boolean canCreateUser(CreateUserRequestDto createUserRequest) {
        return hasPrivilege(Privilege.USER_CREATE)
                && hasPrivileges(createUserRequest.getPrivileges()); // Only allow setting privileges that the authenticated user has
    }
    
    public boolean canReadUsers() {
        return hasPrivilege(Privilege.USER_READ);
    }

    public boolean canReadUser(UUID userId) {
        return hasGrantedResource(ResourceType.USER, userId.toString()) // Allows users to read them self, even if reading other users is not allowed
                || (hasGrantedResource(ResourceType.TENANT, getTenantIdFromUser(userId)) && hasPrivilege(Privilege.USER_READ));
    }

    public boolean canUpdateUser(UUID userId, UpdateUserRequestDto updateUserRequest) {
        return (hasGrantedResource(ResourceType.USER, userId.toString()) // Allows users to update themselves, even if updating other users is not allowed
                || (hasGrantedResource(ResourceType.TENANT, getTenantIdFromUser(userId)) && hasPrivilege(Privilege.USER_UPDATE) && isNotOwner(userId)))
                && hasPrivileges(updateUserRequest.getPrivileges()); // Only allow setting privileges that the authenticated user has
    }

    public boolean canDeleteUser(UUID userId) {
        return hasGrantedResource(ResourceType.TENANT, getTenantIdFromUser(userId)) && hasPrivilege(Privilege.USER_DELETE) && isNotOwner(userId);
    }

    public boolean canReadTenantSettings() {
        return isLoggedIn(); // Anyone can read their own tenant settings
    }

    public boolean canUpdateTenantSettings() {
        return hasPrivilege(Privilege.TENANT_MANAGEMENT);
    }

    public List<UUID> getGrantedBranchIds() {
        Optional<AuthUserDetailsSto> userDetails = authenticationFacade.getAuthenticatedUserDetails();
        return userDetails.map(user -> user.getGrantedResources()
                        .stream()
                        .filter(grantedResourceSto -> grantedResourceSto.getType() == ResourceType.BRANCH)
                        .map(grantedResourceSto -> UUID.fromString(grantedResourceSto.getKey()))
                        .toList())
                .orElse(List.of());
    }

    private boolean hasPrivileges(Collection<Privilege> privileges) {
        if (isEmpty(privileges)) {
            return true;
        }
        Optional<AuthUserDetailsSto> userDetails = authenticationFacade.getAuthenticatedUserDetails();
        List<String> authenticatedUserPrivileges = userDetails
                .map(user -> user.getAuthorities()
                        .stream()
                        .map(SimpleGrantedAuthority::getAuthority)
                        .toList())
                .orElse(List.of());

        boolean decision = privileges.stream().allMatch(privilege -> authenticatedUserPrivileges.contains(privilege.name()));
        log.info("Checked privileges {} on user {} with result {}", privileges, userDetails, decision);
        return decision;
    }

    private boolean isNotOwner(UUID userId) {
        return Role.OWNER != userService.getById(userId).getRole();
    }

    private boolean hasPrivilege(Privilege privilege) {
        Optional<AuthUserDetailsSto> userDetails = authenticationFacade.getAuthenticatedUserDetails();
        boolean decision = userDetails.map(user -> user.getAuthorities()
                        .stream()
                        .map(SimpleGrantedAuthority::getAuthority)
                        .anyMatch(grantedAuthority -> Objects.equals(grantedAuthority, privilege.name())))
                .orElse(false);

        log.info("Checked privilege {} on user {} with result {}", privilege, userDetails, decision);
        return decision;
    }

    private boolean hasGrantedResource(ResourceType type, String key) {
        Optional<AuthUserDetailsSto> userDetails = authenticationFacade.getAuthenticatedUserDetails();
        boolean decision = userDetails.map(user -> user.getGrantedResources()
                        .stream()
                        .anyMatch(grantedResourceSto -> key == null
                                || grantedResourceSto.getType() == type
                                && grantedResourceSto.getKey().equals(key)))
                .orElse(false);
        log.info("Checked granted resource {}:{} on user {} with result {}", type, key, userDetails, decision);
        return decision;
    }

    private String getTenantIdFromUser(UUID userId) {
        return userService.findTenantId(userId)
                .map(UUID::toString)
                .orElse(null);
    }

    private String getTenantIdFromEmployee(UUID employeeId) {
        return employeeService.findTenantId(employeeId)
                .map(UUID::toString)
                .orElse(null);
    }

    private String getTenantIdFromCustomer(UUID customerId) {
        return customerService.findTenantId(customerId)
                .map(UUID::toString)
                .orElse(null);
    }

    private String getTenantIdFromBranch(UUID branchId) {
        return branchService.findTenantId(branchId)
                .map(UUID::toString)
                .orElse(null);
    }
}
