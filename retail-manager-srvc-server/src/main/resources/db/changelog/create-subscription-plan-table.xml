<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.6.xsd">

    <changeSet id="create-table-subscription-plan" author="ggreco">
        <createTable tableName="subscription_plan">
            <column name="id" type="uuid">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="name" type="varchar">
                <constraints nullable="false"/>
            </column>
            <column name="price_monthly" type="decimal(10,2)">
                <constraints nullable="false"/>
            </column>
            <column name="limits" type="jsonb">
                <constraints nullable="true"/>
            </column>

            <column name="create_date" type="timestamp with timezone">
                <constraints nullable="false"/>
            </column>
            <column name="update_date" type="timestamp with timezone">
                <constraints nullable="false"/>
            </column>
            <column name="version" type="bigint">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <createIndex tableName="subscription_plan" indexName="subscription_plan_name_idx">
            <column name="name"/>
        </createIndex>

        <rollback>
            <dropTable tableName="subscription_plan"/>
        </rollback>
    </changeSet>

</databaseChangeLog>
